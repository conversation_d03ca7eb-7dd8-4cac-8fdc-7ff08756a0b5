package sip

import (
	"context"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"

	"github.com/google/uuid"
	"github.com/panjjo/gosip/sip/s"
)

// Server represents SIP server
type Server struct {
	config       *config.ServerConfig
	stateManager *state.Manager
	server       *sip.Server
	logger       *slog.Logger
}

// NewServer creates a new SIP server
func NewServer(cfg *config.ServerConfig, stateManager *state.Manager) *Server {
	return &Server{
		config:       cfg,
		stateManager: stateManager,
		logger:       slog.Default(),
	}
}

// Start starts the SIP server
func (srv *Server) Start(ctx context.Context) error {
	// Create SIP server
	server := sip.NewServer()
	srv.server = server

	// Register handlers
	server.RegistHandler(sip.REGISTER, srv.handleRegister)
	server.RegistHandler(sip.MESSAGE, srv.handleMessage)
	server.RegistHandler(sip.INVITE, srv.handleInvite)
	server.RegistHandler(sip.BYE, srv.handleBye)

	// Start listening
	listenAddr := fmt.Sprintf("%s:%d", srv.config.SIPIP, srv.config.SIPPort)

	go func() {
		srv.logger.Info("Starting SIP server", "address", listenAddr)
		server.ListenUDPServer(listenAddr)
	}()

	srv.logger.Info("SIP server started", "address", listenAddr)
	return nil
}

// Stop stops the SIP server
func (srv *Server) Stop() {
	// panjjo/gosip doesn't have explicit stop method
	// The server will stop when the process exits
	srv.logger.Info("SIP server stopping")
}

// handleRegister handles REGISTER requests
func (srv *Server) handleRegister(req *sip.Request, tx *sip.Transaction) {
	// Extract platform information
	fromHeader, ok := req.From()
	if !ok {
		srv.sendResponse(tx, req, 400, "Bad Request")
		return
	}

	platformID := fromHeader.Address.User().String()
	sipURI := fromHeader.Address.String()

	// Extract expires
	expires := 3600 // default
	if expiresHeaders := req.GetHeaders("Expires"); len(expiresHeaders) > 0 {
		if exp, err := strconv.Atoi(expiresHeaders[0].String()); err == nil {
			expires = exp
		}
	}

	// Extract IP and port from Via header
	viaHeader, ok := req.ViaHop()
	var ip string
	var port int
	if ok {
		ip = viaHeader.Host
		if viaHeader.Port != nil {
			port = int(*viaHeader.Port)
		} else {
			port = 5060
		}
	}

	// Register platform
	platform := &models.Platform{
		ID:      platformID,
		SIPURI:  sipURI,
		Expires: expires,
		IP:      ip,
		Port:    port,
	}
	srv.stateManager.RegisterPlatform(platform)

	// Send 200 OK response
	srv.sendResponse(tx, req, 200, "OK")
	srv.logger.Info("Platform registered", "id", platformID, "uri", sipURI)
}

// handleMessage handles MESSAGE requests
func (srv *Server) handleMessage(req *sip.Request, tx *sip.Transaction) {
	// Send 200 OK first
	srv.sendResponse(tx, req, 200, "OK")

	// Parse message body
	body := string(req.Body())
	if body == "" {
		return
	}

	srv.logger.Debug("Received MESSAGE", "body", body)

	// Handle different message types based on content
	if strings.Contains(body, "Keepalive") {
		srv.handleKeepalive(req)
	} else if strings.Contains(body, "Catalog") {
		srv.handleCatalogResponse(req, body)
	}
}

// handleKeepalive handles keepalive messages
func (srv *Server) handleKeepalive(req *sip.Request) {
	fromHeader, ok := req.From()
	if ok {
		platformID := fromHeader.Address.User().String()
		srv.stateManager.UpdatePlatformLastSeen(platformID)
		srv.logger.Debug("Keepalive received", "platform_id", platformID)
	}
}

// handleCatalogResponse handles catalog response messages
func (srv *Server) handleCatalogResponse(req *sip.Request, body string) {
	// This is a simplified implementation
	// In a real implementation, you would parse the XML body
	// and extract device information
	srv.logger.Debug("Catalog response received", "body", body)

	// TODO: Parse XML and extract devices
	// For now, return empty device list
	devices := []models.Device{}

	// Extract SN from body to match with pending request
	// This is simplified - you would parse XML properly
	sn := "default"

	if err := srv.stateManager.SendCatalogResponse(sn, devices); err != nil {
		srv.logger.Error("Failed to send catalog response", "error", err)
	}
}

// handleInvite handles INVITE requests
func (srv *Server) handleInvite(req *sip.Request, tx *sip.Transaction) {
	// Send 200 OK response
	srv.sendResponse(tx, req, 200, "OK")
	srv.logger.Info("INVITE request handled")
}

// handleBye handles BYE requests
func (srv *Server) handleBye(req *sip.Request, tx *sip.Transaction) {
	// Send 200 OK response
	srv.sendResponse(tx, req, 200, "OK")
	srv.logger.Info("BYE request handled")
}

// sendResponse sends a SIP response
func (srv *Server) sendResponse(tx *sip.Transaction, req *sip.Request, statusCode int, reasonPhrase string) {
	response := sip.NewResponseFromRequest("", req, statusCode, reasonPhrase, nil)

	if err := tx.Respond(response); err != nil {
		srv.logger.Error("Failed to send SIP response", "error", err)
	}
}

// SendCatalogQuery sends a catalog query to platform
func (srv *Server) SendCatalogQuery(platformID string) (string, error) {
	_, exists := srv.stateManager.GetPlatform(platformID)
	if !exists {
		return "", fmt.Errorf("platform %s not found", platformID)
	}

	// Generate unique SN
	sn := uuid.New().String()[:8]

	// TODO: Implement SIP MESSAGE request for catalog query using panjjo/gosip
	// This is a placeholder implementation
	srv.logger.Info("Catalog query requested", "platform_id", platformID, "sn", sn)

	// For now, return empty device list after a short delay
	go func() {
		time.Sleep(1 * time.Second)
		devices := []models.Device{} // Empty list for now
		srv.stateManager.SendCatalogResponse(sn, devices)
	}()

	return sn, nil
}

// SendInvite sends an INVITE request for video stream
func (srv *Server) SendInvite(gbID, receiveIP string, receivePort int) (*models.StreamSession, error) {
	// Check if device exists
	_, exists := srv.stateManager.GetDevice(gbID)
	if !exists {
		return nil, fmt.Errorf("device not found")
	}

	// Generate session info
	sessionID := uuid.New().String()
	ssrc := fmt.Sprintf("%d", time.Now().Unix()%1000000)

	// TODO: Implement SIP INVITE request using panjjo/gosip
	// This is a placeholder implementation
	srv.logger.Info("INVITE requested", "gb_id", gbID, "receive_ip", receiveIP, "receive_port", receivePort)

	// Create session
	session := &models.StreamSession{
		SessionID:   sessionID,
		GBID:        gbID,
		SSRC:        ssrc,
		Destination: fmt.Sprintf("%s:%d", receiveIP, receivePort),
	}

	srv.stateManager.CreateSession(session)
	return session, nil
}

// SendPTZControl sends PTZ control command
func (srv *Server) SendPTZControl(gbID, command string, speed int) error {
	// Check if device exists
	_, exists := srv.stateManager.GetDevice(gbID)
	if !exists {
		return fmt.Errorf("device not found")
	}

	// TODO: Implement SIP MESSAGE request for PTZ control using panjjo/gosip
	// This is a placeholder implementation
	srv.logger.Info("PTZ control requested", "gb_id", gbID, "command", command, "speed", speed)

	return nil
}
